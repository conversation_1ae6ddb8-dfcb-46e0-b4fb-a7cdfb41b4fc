@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #1f2937;
}

@layer utilities {
  .font-inter {
    font-family: "Inter", sans-serif;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
  }

  .animate-bubble-rise {
    animation: bubbleRise 10s linear infinite;
  }

  .animate-bubble-rise-slow {
    animation: bubbleRise 15s linear infinite;
  }

  .animate-bubble-rise-fast {
    animation: bubbleRise 8s linear infinite;
  }

  .animate-bubble-rise-delayed {
    animation: bubbleRise 12s linear infinite;
  }

  .animate-bubble-sway {
    animation: bubbleSway 8s ease-in-out infinite;
  }

  .animate-bubble-sway-reverse {
    animation: bubbleSwayReverse 10s ease-in-out infinite;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bubbleRise {
  0% {
    transform: translateY(100vh) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) scale(1.2);
    opacity: 0;
  }
}

@keyframes bubbleSway {
  0%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(15px);
  }
  50% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(20px);
  }
}

@keyframes bubbleSwayReverse {
  0%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-20px);
  }
  50% {
    transform: translateX(15px);
  }
  75% {
    transform: translateX(-10px);
  }
}

/* Smooth scrolling for older browsers */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* **************** Letras inteligencia artificial del HeroSection **************************/

@layer utilities {
  /* --- Clase para el texto azul, ahora con un brillo más suave --- */
  .text-title-glow {
    color: #0044ff;
    text-shadow: 0 0 15px rgba(25, 135, 255, 0.7),
      0 0 40px rgba(25, 135, 255, 0.7);
  }

  /* --- LA SOLUCIÓN IMPECABLE: SIN ARTEFACTOS --- */
  .text-flawless-gradient {
    position: relative;
    color: transparent;
    font-family: 'Arial Black', 'Helvetica', 'Verdana', sans-serif;
    font-weight: 900;
    background: linear-gradient(
      90deg,
      rgb(0, 177, 142) 0%,
      rgb(51, 126, 169) 25%,
      rgb(102, 91, 198) 50%,
      rgb(154, 48, 179) 75%,
      rgb(205, 5, 255) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.7);
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.0), 0 0 1px rgb(255, 255, 255, 0.1);
  }

  .text-flawless-gradient::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      transparent 10%,
      rgba(255, 255, 255, 0.8) 30%,
      transparent 50%,
      transparent 70%
    );
    background-size: 200% 100%;
    background-position: -100% center;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 0.5px rgba(0, 0, 0, 0.6);
    animation: shine-sweep 10s ease-in-out infinite;
    animation-delay: 1s;
  }

  @keyframes shine-sweep {
    0% {
      background-position: -100% center;
    }
    50% {
      background-position: 100% center;
    }
    100% {
      background-position: -100% center;
    }
  }
}
